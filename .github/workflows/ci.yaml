name: ci

on:
  pull_request:
  push:
    branches:
      - master

permissions:
  contents: read

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - name: checkout
        uses: actions/checkout@f43a0e5ff2bd294095638e18286ca9a3d1956744 # v3.6.0
        with:
          fetch-depth: 0

      - name: setup go
        uses: actions/setup-go@6edd4406fa81c3da01a34fa6f6343087c207a568 # v3.5.0
        with:
          go-version-file: go.mod
          cache: true

      - name: setup node
        uses: actions/setup-node@8c91899e586c5b171469028077307d293428b516 # v3.5.1
        with:
          node-version-file: ".nvmrc"
          cache: "npm"
          cache-dependency-path: webapp/package-lock.json

      - name: lint server
        run: make check-style-go

      - name: lint webapp
        run: make check-style-webapp

  test:
    runs-on: ubuntu-latest
    steps:
      - name: checkout
        uses: actions/checkout@f43a0e5ff2bd294095638e18286ca9a3d1956744 # v3.6.0
        with:
          fetch-depth: 0

      - name: setup go
        uses: actions/setup-go@6edd4406fa81c3da01a34fa6f6343087c207a568 # v3.5.0
        with:
          go-version-file: go.mod
          cache: true

      - name: setup node
        uses: actions/setup-node@8c91899e586c5b171469028077307d293428b516 # v3.5.1
        with:
          node-version-file: ".nvmrc"
          cache: "npm"
          cache-dependency-path: webapp/package-lock.json

      - name: test
        run: make test
