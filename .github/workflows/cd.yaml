name: cd

on:
  workflow_run:
    workflows: ["ci"]
    branches-ignore: ["*"]
    types:
      - completed
  push:
    tags:
      - "v*"

permissions:
  contents: read

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: checkout
        uses: actions/checkout@f43a0e5ff2bd294095638e18286ca9a3d1956744 # v3.6.0
        with:
          fetch-depth: 0

      - name: setup go
        uses: actions/setup-go@6edd4406fa81c3da01a34fa6f6343087c207a568 # v3.5.0
        with:
          go-version-file: go.mod
          cache: true

      - name: setup node
        uses: actions/setup-node@8c91899e586c5b171469028077307d293428b516 # v3.5.1
        with:
          node-version-file: ".nvmrc"
          cache: "npm"
          cache-dependency-path: webapp/package-lock.json

      - name: dist
        shell: bash
        run: make dist
        env:
          CGO_ENABLED: "0"

      - name: upload
        uses: actions/upload-artifact@a8a3f3ad30e3422c9c7b888a15615d19a852ae32 # v3.1.3
        with:
          name: dist
          path: dist/*.tar.gz
          if-no-files-found: error
          retention-days: 14
