.styled-accent {
    accent-color: var(--button-bg);
    margin: -18px !important;
}
svg{
    
}
option{
    &:hover {
        background: #00987e;
    }
}
.custom-select {
    position: relative;
    width: 100%;
    
    .selected-option {
        cursor: pointer;
        border-radius: 4px;

    }
    
    .options-list {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background-color: var(--sidebar-header-bg);
        border: 1px solid #ccc;
        border-radius: 4px;
        max-height: 200px;
        overflow-y: auto;
        z-index: 1000;
        margin-top: 4px;
        color: var(--sidebar-text);
    }
    
    .option {
        padding: 8px;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 8px;
        
        &:hover {
            background: #00987e;
        }
    }
    
    .option-content {
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    img {
        border-radius: 50%;
        object-fit: cover;
    }
}

.custom-select {
    position: relative;
    width: 100%;

    .selected-option {
        padding: 8px;
        border: 1px solid #ccc;
        border-radius: 4px;
        cursor: pointer;
        display: flex;
        align-items: center;
        background: transparent;

        .option-content {
            display: flex;
            align-items: center;
            width: 100%;
        }
    }

    .options-container {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: transparent;
        border: 1px solid #ccc;
        border-radius: 4px;
        margin-top: 4px;
        z-index: 1000;

        .search-input {
            width: 100%;
            padding: 8px;
            border: none;
            border-bottom: 1px solid #eee;
            outline: none;
            font-family: 'GraphikArabic';

            &:focus {
                border-bottom-color: #0fcea5;
            }
        }

        .options-list {
            max-height: 200px;
            overflow-y: auto;

            .option {
                padding: 8px;
                cursor: pointer;
                display: flex;
                align-items: center;
                transition: background-color 0.2s;
                font-family: 'GraphikArabic';

                &:hover {
                    background-color: #00987e;
                }

                img {
                    margin-right: 8px;
                }
            }
        }
    }
}