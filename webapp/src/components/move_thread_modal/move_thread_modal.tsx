import React from 'react';

import {Modal} from 'react-bootstrap';
import Form from 'react-bootstrap/Form';

import {Team} from 'mattermost-redux/types/teams';
import {Channel} from 'mattermost-redux/types/channels';

import {MessageActionTypeCopy} from '../../types/actions';

import '../style.scss';
// eslint-disable-next-line import/order
import {Client4} from 'mattermost-redux/client';

interface Props {
    visible: boolean;
    postID: string;
    message: string;
    threadCount: number;
    copyThread: Function;
    getMyTeams: Function;
    getChannelsForTeam: Function;
    getChannels: Function;
    closeMoveThreadModal: Function;
}

type State = {
    allTeams: Array<Team>;
    channelsInTeam: Array<Channel>;
    channelsI: Array<Channel>;
    selectedTeam: string;
    selectedChannel: string;
    moveThreadButtonText: string;
    actionWord: string;
    processing: boolean;
    selectedOption: 'channels' | 'users';
    users: Array<Channel>;
    isOpen: boolean;
    searchQuery: string;
    showUsername: boolean; // Add showUsername to state
}

export default class MoveThreadModal extends React.PureComponent<Props, State> {
    constructor(props: Props) {
        super(props);

        this.state = {
            allTeams: Array<Team>(),
            channelsInTeam: Array<Channel>(),
            channelsI: Array<Channel>(),
            selectedTeam: '',
            selectedChannel: '',
            moveThreadButtonText: 'نسخ الموضوع',
            actionWord: 'نسخ',
            processing: false,
            selectedOption: 'channels',
            users: Array<Channel>(),
            isOpen: false,
            searchQuery: '',
            showUsername: true, // Default to showing username
        };
    }

    componentDidMount() {
        if (this.state.selectedOption === 'channels') {
            this.loadTeams();
        }
    }

    private loadTeams = async () => {
        const myTeams = await this.props.getMyTeams();

        let firstTeamID = '';
        let firstChannelID = '';
        let channels = Array<Channel>();
        if (myTeams.length > 0) {
            const firstTeam = myTeams[0];
            firstTeamID = firstTeam.id;
            channels = await this.props.getChannelsForTeam(firstTeamID);
            if (channels.length > 0) {
                const firstChannel = channels[0];
                firstChannelID = firstChannel.id;
            }
        }
        let channel = Array<Channel>();
        if (myTeams.length > 0) {
            const firstTeam = myTeams[0];
            firstTeamID = firstTeam.id;
            channel = await this.props.getChannels(firstTeamID);
            if (channel.length > 0) {
                const firstChannel = channel[0];
                firstChannelID = firstChannel.id;
            }
        }

        this.setState({
            allTeams: myTeams,
            channelsInTeam: channels,
            channelsI: channel,
            selectedTeam: firstTeamID,
            selectedChannel: firstChannelID,
        });
    }

    private handleTeamSelectChange = async (event: React.ChangeEvent<HTMLSelectElement>) => {
        const teamID = event.target.value;
        const channels = await this.props.getChannelsForTeam(teamID);
        const channel = await this.props.getChannels(teamID);
        let firstChannelID = '';
        if (channels.length > 0) {
            const firstChannel = channels[0];
            firstChannelID = firstChannel.id;
        }

        this.setState({
            selectedTeam: teamID,
            selectedChannel: firstChannelID,
            channelsInTeam: channels,
            channelsI: channel,
        });
    }

    private handleChannelSelectChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
        this.setState({selectedChannel: event.target.value});
    }

    private handleUserSelectChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
        this.setState({selectedChannel: event.target.value});
    }

    private handleToggleUsername = (event: React.ChangeEvent<HTMLInputElement>) => {
        this.setState({showUsername: event.target.checked});
    };

    private handleOnClick = async (event: React.MouseEvent) => {
        if (event && event.preventDefault) {
            event.preventDefault();
        }

        this.setState({processing: true});
        await this.props.copyThread(this.props.postID, this.state.selectedChannel, this.state.showUsername);
        this.props.closeMoveThreadModal();
        this.setState({processing: false});
    };

    private handleClose = async (event: React.MouseEvent) => {
        if (event && event.preventDefault) {
            event.preventDefault();
        }

        this.props.closeMoveThreadModal();
    };

    public render() {
        let disabled = this.state.processing;
        if (this.props.postID === '' || this.state.selectedChannel === '') {
            disabled = true;
        }

        let title = 'إعادة توجيه الرسالة';
        let moveMessage = 'نسخ هذه الرسالة؟';
        if (this.props.threadCount > 1) {
            title = '  إعادة توجيه الرسالة';
            moveMessage = 'نسخ هذا الموضوع المكون من ' + this.props.threadCount + ' رسائل؟';
        }

        let actionButtonText = this.state.moveThreadButtonText;
        if (this.state.processing) {
            actionButtonText = 'جاري النسخ...';
        }

        return (
            <Modal
                dialogClassName='modal--scroll'
                show={this.props.visible}
                onHide={this.handleClose}
                onExited={this.handleClose}
                bsSize='large'
                backdrop='static'
            >
                <Modal.Header closeButton={true}>
                    <h1 className='modal-title'>{title}</h1>
                </Modal.Header>
                <Modal.Body>
                    <Form>
                        <Form.Group>
                            <Form.Label>{'اختر نوع النقل'}</Form.Label>
                            <Form.Control
                                as='select'
                                onChange={(e) => this.setState({selectedOption: e.target.value as 'channels' | 'users'})}
                                value={this.state.selectedOption}
                            >
                                <option
                                    className='option'
                                    value='channels'
                                >{'القنوات'}</option>
                                <option
                                    className='option'
                                    value='users'
                                >{'المستخدمين'}</option>
                            </Form.Control>
                        </Form.Group>
                        <Form.Group className='mt-3 d-flex align-items-center gap-2'>
                            <Form.Check
                                type='switch'
                                id='username-toggle'
                                label='إظهار اسم المستخدم في الرسالة'
                                checked={this.state.showUsername}
                                onChange={this.handleToggleUsername}
                            />
                        </Form.Group>

                        {this.state.selectedOption === 'channels' ? (
                            <>
                                <Form.Group>
                                    <Form.Label>{'الفريق'}</Form.Label>
                                    <Form.Control
                                        as='select'
                                        onChange={this.handleTeamSelectChange}
                                        value={this.state.selectedTeam}
                                    >
                                        {this.state.allTeams.map((team) => (
                                            <option
                                                className='option'
                                                key={team.id}
                                                id={team.id}
                                                value={team.id}
                                            >
                                                {team.display_name}
                                            </option>
                                        ))}
                                    </Form.Control>
                                </Form.Group>
                                <Form.Group>
                                    <Form.Label>{'المجموعات'}</Form.Label>
                                    <Form.Control
                                        as='select'
                                        onChange={this.handleChannelSelectChange}
                                        value={this.state.selectedChannel}
                                        disabled={this.state.selectedTeam === ''}
                                    >
                                        {this.state.channelsInTeam.map((channel) => (
                                            <option
                                                className='option'
                                                key={channel.id}
                                                id={channel.id}
                                                value={channel.id}
                                            >
                                                {channel.display_name}
                                            </option>
                                        ))}
                                    </Form.Control>
                                </Form.Group>
                            </>
                        ) : (
                            <Form.Group>
                                <Form.Label>{'المستخدمين'}</Form.Label>
                                <div className='custom-select'>
                                    <div
                                        className='selected-option'
                                        onClick={() => this.setState({isOpen: !this.state.isOpen})}
                                    >
                                        {this.state.selectedChannel ? (
                                            <div className='option-content'>
                                                <img
                                                    src={Client4.getProfilePictureUrl(this.state.channelsI.find((c) => c.id === this.state.selectedChannel)?.scheme_id, 0)}
                                                    alt=''
                                                    style={{width: '24px', height: '24px', borderRadius: '50%', marginRight: '8px'}}
                                                />
                                                <span>
                                                    {this.state.channelsI.find((c) => c.id === this.state.selectedChannel)?.display_name}
                                                </span>
                                            </div>
                                        ) : (
                                            <span> {' اختر مستخدم '}</span>
                                        )}
                                    </div>
                                    {this.state.isOpen && (
                                        <div className='options-container'>
                                            <input
                                                type='text'
                                                className='search-input'
                                                placeholder='ابحث عن مستخدم...'
                                                value={this.state.searchQuery}
                                                onChange={(e) => this.setState({searchQuery: e.target.value})}
                                                onClick={(e) => e.stopPropagation()}
                                                onKeyDown={(e) => {
                                                    if (e.key === 'Enter') {
                                                        e.preventDefault();
                                                        const filteredUsers = this.state.channelsI.filter((channel) =>
                                                            channel.display_name.toLowerCase().includes(this.state.searchQuery.toLowerCase()),
                                                        );
                                                        if (filteredUsers.length > 0) {
                                                            const firstUser = filteredUsers[0];
                                                            this.setState({
                                                                selectedChannel: firstUser.id,
                                                                isOpen: false,
                                                                searchQuery: '',
                                                            });
                                                            this.handleUserSelectChange({target: {value: firstUser.id}} as any);
                                                        }
                                                    }
                                                }}
                                            />
                                            <div className='options-list'>
                                                {this.state.channelsI.
                                                    filter((channel) =>
                                                        channel.display_name.toLowerCase().includes(this.state.searchQuery.toLowerCase()),
                                                    ).
                                                    map((channel) => (
                                                        <div
                                                            key={channel.id}
                                                            className='option'
                                                            onClick={() => {
                                                                this.setState({
                                                                    selectedChannel: channel.id,
                                                                    isOpen: false,
                                                                    searchQuery: '',
                                                                });
                                                                this.handleUserSelectChange({target: {value: channel.id}} as any);
                                                            }}
                                                        >
                                                            <img
                                                                src={Client4.getProfilePictureUrl(channel.scheme_id, 0)}
                                                                alt=''
                                                                style={{width: '24px', height: '24px', borderRadius: '50%'}}
                                                            />
                                                            <span>{channel.display_name}</span>
                                                        </div>
                                                    ))
                                                }
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </Form.Group>
                        )}

                        <Form.Group>
                            <Form.Label>{'الرسالة الجذرية للموضوع'}</Form.Label>
                            <textarea
                                style={{resize: 'none'}}
                                className='form-control'
                                rows={5}
                                value={this.props.message}
                                disabled={true}
                                readOnly={true}
                            />
                        </Form.Group>
                    </Form>
                </Modal.Body>
                <Modal.Footer>
                    <button
                        id='footerClose'
                        className='btn btn-tertiary'
                        onClick={this.handleClose}
                    >
                        {'إلغاء'}
                    </button>
                    <button
                        id='saveSetting'
                        className='btn btn-primary'
                        style={{width: '130px'}}
                        onClick={this.handleOnClick}
                        disabled={disabled}
                    >
                        {actionButtonText}
                    </button>
                </Modal.Footer>
            </Modal>
        );
    }
}