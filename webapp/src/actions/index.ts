import {GlobalState} from 'mattermost-redux/types/store';

import {Channel} from 'mattermost-redux/types/channels';
import {Team} from 'mattermost-redux/types/teams';
import {getTeam, getTeamMemberships} from 'mattermost-redux/selectors/entities/teams';
import {Client4} from 'mattermost-redux/client';

import {UserProfile, UserProfileWithLastViewAt} from 'mattermost-redux/types/users';

import {RECEIVED_PLUGIN_SETTINGS} from '../types/wrangler';
import {OPEN_MOVE_THREAD_MODAL, CLOSE_MOVE_THREAD_MODAL} from '../types/ui';
import {INITIALIZE_ATTACH_POST, FINALIZE_ATTACH_POST, RichPost} from '../types/attach';
import {INITIALIZE_MERGE_THREAD, FINALIZE_MERGE_THREAD} from '../types/merge';

import Client from '../client';
import {INITIALIZE_COPY_TO_CHANNEL, FINALIZE_COPY_TO_CHANNEL} from 'src/types/channel';

export type GetStateFunc = () => GlobalState;
export type ActionResult = {
    data: any; //eslint-disable-line @typescript-eslint/no-explicit-any
} | {
    error: any; //eslint-disable-line @typescript-eslint/no-explicit-any
};
export type DispatchFunc = (action: Action, getState?: GetStateFunc | null) => Promise<ActionResult>;
export type ActionFunc = (dispatch: DispatchFunc, getState: GetStateFunc) => Promise<ActionResult | ActionResult[]> | ActionResult;
export type Action = ActionFunc | GenericAction;
export type GenericAction = {
    type: string;
    [extraProps: string]: any; //eslint-disable-line @typescript-eslint/no-explicit-any
};

export function openMoveThreadModal(postID: string): ActionFunc {
    return async (dispatch: DispatchFunc) => {
        dispatch({
            type: OPEN_MOVE_THREAD_MODAL,
            post_id: postID,
        });

        return {data: postID};
    };
}

export function closeMoveThreadModal(): ActionFunc {
    return async (dispatch: DispatchFunc) => {
        dispatch({
            type: CLOSE_MOVE_THREAD_MODAL,
        });

        return {data: null};
    };
}

export function startAttachingPost(post: RichPost): ActionFunc {
    return async (dispatch: DispatchFunc) => {
        dispatch({
            type: INITIALIZE_ATTACH_POST,
            post,
        });

        return {data: null};
    };
}

export function finishAttachingPost(): ActionFunc {
    return async (dispatch: DispatchFunc) => {
        dispatch({
            type: FINALIZE_ATTACH_POST,
        });

        return {data: null};
    };
}

export function startCopyToChannel(channel: Channel): ActionFunc {
    return async (dispatch: DispatchFunc) => {
        dispatch({
            type: INITIALIZE_COPY_TO_CHANNEL,
            channel,
        });

        return {data: null};
    };
}

export function finishCopyToChannel(): ActionFunc {
    return async (dispatch: DispatchFunc) => {
        dispatch({
            type: FINALIZE_COPY_TO_CHANNEL,
        });

        return {data: null};
    };
}

export function startMergingThread(post: RichPost): ActionFunc {
    return async (dispatch: DispatchFunc) => {
        dispatch({
            type: INITIALIZE_MERGE_THREAD,
            post,
        });

        return {data: null};
    };
}

export function finishMergingThread(): ActionFunc {
    return async (dispatch: DispatchFunc) => {
        dispatch({
            type: FINALIZE_MERGE_THREAD,
        });

        return {data: null};
    };
}

export function getSettings(): ActionFunc {
    return async (dispatch: DispatchFunc) => {
        const {data: settings, error} = await Client.getSettings();
        if (error) {
            return {error};
        }

        dispatch({
            type: RECEIVED_PLUGIN_SETTINGS,
            settings,
        });

        return {data: settings};
    };
}

export function getMyTeams(): Function {
    return async (_: DispatchFunc, getState: GetStateFunc) => {
        const myTeamMemberships = getTeamMemberships(getState());
        const myTeams = Array<Team>();
        Object.keys(myTeamMemberships).forEach((id) => {
            const team = getTeam(getState(), id);

            // There are cases where a team may not be loaded into redux even
            // though they exist. This seems most likely to occur when many
            // teams exist on the Mattermost instance. This will protect against
            // crashes, but looking up missing teams will require further
            // investigation.
            if (typeof team !== 'undefined') {
                myTeams.push(team);
            }
        });

        return myTeams;
    };
}

export function getChannelsForTeam(teamID: string): Function {
    return async () => {
        let allMyChannelsInTeam = Array<Channel>();
        allMyChannelsInTeam = await Client4.getMyChannels(teamID);

        const myOpenAndPrivateChannelsInTeam = Array<Channel>();
        allMyChannelsInTeam.forEach((channel) => {
            if (channel.type === 'O' || channel.type === 'P') {
                myOpenAndPrivateChannelsInTeam.push(channel);
            }
        });
        return myOpenAndPrivateChannelsInTeam;
    };
}

export function getChannels(teamID: string): Function {
    return async () => {
        const allMyChannelsInTeam = await Client4.getMyChannels(teamID);
        const myOpenAndPrivateChannelsInTeam: Channel[] = [];
        const me = await Client4.getMe();

        // Helper function to get a team member's info
        async function getTeamMate(id: string): Promise<UserProfile | null> {
            try {
                const user = await Client4.getUser(id);
                return user;
            } catch (error) {
                // eslint-disable-next-line no-console
                console.error('Error fetching user:', error);
                return null;
            }
        }

        // Use for...of instead of forEach to handle async operations properly
        for (const channel of allMyChannelsInTeam) {
            // if (channel.type === 'O' || channel.type === 'P') {
            //     myOpenAndPrivateChannelsInTeam.push(channel);
            // } else {
            if (channel.type === 'D') {
                const names = channel.name.split('__');
                const teamMateId = me.id === names[0] ? names[1] : names[0]; // Get the other user ID

                // eslint-disable-next-line no-await-in-loop
                const user = await getTeamMate(teamMateId); // Wait for the result
                if (user) {
                    // eslint-disable-next-line eqeqeq
                    if (user.first_name == '' && user.last_name == '') {
                        channel.display_name = user.username;
                    } else {
                        channel.display_name = user.first_name + ' ' + user.last_name;
                    }
                    channel.scheme_id = user.id;
                    myOpenAndPrivateChannelsInTeam.push(channel);
                }
            }
        }

        return myOpenAndPrivateChannelsInTeam;
    };
}

export function moveThread(postID: string, channelID: string, showRootMessage: boolean, silent: boolean): ActionFunc {
    return async (dispatch: DispatchFunc, getState: GetStateFunc) => {
        const command = `/wrangler move thread ${postID} ${channelID} --show-root-message-in-summary=${showRootMessage} --silent=${silent}`;
        await Client.clientExecuteCommand(getState, command);

        return {data: null};
    };
}

export function copyThread(postID: string, channelID: string, showUsername = true): ActionFunc {
    return async (dispatch: DispatchFunc, getState: GetStateFunc) => {
        const usernameFlag = showUsername ? '' : '--no-username';
        const command = `/wrangler copy thread ${postID} ${channelID} ${usernameFlag}`.trim();
        await Client.clientExecuteCommand(getState, command);

        return {data: null};
    };
}

export function attachMessage(postToBeAttachedID: string, postToAttachToID: string): ActionFunc {
    return async (dispatch: DispatchFunc, getState: GetStateFunc) => {
        const command = `/wrangler attach message ${postToBeAttachedID} ${postToAttachToID}`;
        await Client.clientExecuteCommand(getState, command);

        return {data: null};
    };
}

export function mergeThread(postToBeMergedID: string, postToMergeToID: string): ActionFunc {
    return async (dispatch: DispatchFunc, getState: GetStateFunc) => {
        const command = `/wrangler merge thread ${postToBeMergedID} ${postToMergeToID}`;
        await Client.clientExecuteCommand(getState, command);

        return {data: null};
    };
}